// Chat Interface JavaScript
class ChatInterface {
    constructor() {
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');
        this.chatMessages = document.getElementById('chat-messages');
        this.typingIndicator = document.getElementById('typing-indicator');
        this.themeToggle = document.getElementById('theme-toggle');
        this.settingsBtn = document.getElementById('settings-btn');
        this.settingsModal = document.getElementById('settings-modal');
        this.closeSettings = document.getElementById('close-settings');
        this.clearChatBtn = document.getElementById('clear-chat');
        
        this.currentTheme = localStorage.getItem('chat-theme') || 'light';
        this.messages = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.setupAutoResize();
        this.loadSettings();
    }
    
    setupEventListeners() {
        // Send message
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Input validation
        this.messageInput.addEventListener('input', () => this.validateInput());
        
        // Theme toggle
        this.themeToggle.addEventListener('click', () => this.toggleTheme());
        
        // Settings modal
        this.settingsBtn.addEventListener('click', () => this.openSettings());
        this.closeSettings.addEventListener('click', () => this.closeSettingsModal());
        this.settingsModal.addEventListener('click', (e) => {
            if (e.target === this.settingsModal) {
                this.closeSettingsModal();
            }
        });
        
        // Clear chat
        this.clearChatBtn.addEventListener('click', () => this.clearChat());
        
        // Settings changes
        document.getElementById('theme-select').addEventListener('change', (e) => {
            this.setTheme(e.target.value);
        });
        
        document.getElementById('font-size-select').addEventListener('change', (e) => {
            this.setFontSize(e.target.value);
        });
    }
    
    setupTheme() {
        this.setTheme(this.currentTheme);
    }
    
    setupAutoResize() {
        this.messageInput.addEventListener('input', () => {
            this.messageInput.style.height = 'auto';
            this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
        });
    }
    
    validateInput() {
        const text = this.messageInput.value.trim();
        const charCount = this.messageInput.value.length;
        
        // Update character count
        document.querySelector('.char-count').textContent = `${charCount}/2000`;
        
        // Enable/disable send button
        this.sendBtn.disabled = text.length === 0;
        
        // Update character count color
        const charCountEl = document.querySelector('.char-count');
        if (charCount > 1800) {
            charCountEl.style.color = 'var(--error-color)';
        } else if (charCount > 1500) {
            charCountEl.style.color = 'var(--warning-color)';
        } else {
            charCountEl.style.color = 'var(--text-muted)';
        }
    }
    
    sendMessage() {
        const text = this.messageInput.value.trim();
        if (!text) return;
        
        // Add user message
        this.addMessage(text, 'user');
        
        // Clear input
        this.messageInput.value = '';
        this.messageInput.style.height = 'auto';
        this.validateInput();
        
        // Show typing indicator and simulate AI response
        this.showTypingIndicator();
        
        // Simulate AI response after delay
        setTimeout(() => {
            this.hideTypingIndicator();
            this.addMessage(this.generateAIResponse(text), 'agent');
        }, 1500 + Math.random() * 2000);
    }
    
    addMessage(text, sender) {
        // Remove welcome message if it exists
        const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        const messageEl = document.createElement('div');
        messageEl.className = `message ${sender}`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageEl.innerHTML = `
            <div class="message-content">
                ${this.formatMessage(text)}
                <div class="message-time">${timeString}</div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageEl);
        this.scrollToBottom();
        
        // Store message
        this.messages.push({ text, sender, timestamp: now });
    }
    
    formatMessage(text) {
        // Basic text formatting (you can extend this)
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }
    
    generateAIResponse(userMessage) {
        // Simulate AI responses (replace with actual AI integration)
        const responses = [
            "I understand your question. Let me help you with that.",
            "That's an interesting point. Here's what I think...",
            "I can assist you with that. Let me provide some information.",
            "Thank you for your message. I'm here to help.",
            "I see what you're asking about. Let me explain...",
            "That's a great question! Here's my response...",
            "I'd be happy to help you with that request.",
            "Let me process that information and provide you with an answer."
        ];
        
        return responses[Math.floor(Math.random() * responses.length)];
    }
    
    showTypingIndicator() {
        this.typingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        this.typingIndicator.style.display = 'none';
    }
    
    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }
    
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }
    
    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('chat-theme', theme);
        
        // Update theme toggle icon
        const icon = this.themeToggle.querySelector('i');
        icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        
        // Update settings select
        document.getElementById('theme-select').value = theme;
    }
    
    setFontSize(size) {
        document.documentElement.setAttribute('data-font-size', size);
        localStorage.setItem('chat-font-size', size);
    }
    
    openSettings() {
        this.settingsModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
    
    closeSettingsModal() {
        this.settingsModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    
    clearChat() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            this.chatMessages.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>Welcome to AI Agent Chat</h3>
                    <p>I'm here to help you with any questions or tasks. How can I assist you today?</p>
                </div>
            `;
            this.messages = [];
        }
    }
    
    loadSettings() {
        // Load saved settings
        const savedTheme = localStorage.getItem('chat-theme') || 'light';
        const savedFontSize = localStorage.getItem('chat-font-size') || 'medium';
        
        this.setTheme(savedTheme);
        this.setFontSize(savedFontSize);
        
        document.getElementById('theme-select').value = savedTheme;
        document.getElementById('font-size-select').value = savedFontSize;
    }
}

// Initialize the chat interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatInterface();
});

// Add some CSS for font sizes
const fontSizeStyles = document.createElement('style');
fontSizeStyles.textContent = `
    [data-font-size="small"] {
        font-size: 14px;
    }
    [data-font-size="medium"] {
        font-size: 16px;
    }
    [data-font-size="large"] {
        font-size: 18px;
    }
`;
document.head.appendChild(fontSizeStyles);
